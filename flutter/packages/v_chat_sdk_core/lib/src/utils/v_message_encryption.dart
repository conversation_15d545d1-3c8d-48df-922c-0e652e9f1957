// Copyright 2023, the hate<PERSON><PERSON>b project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:encrypt/encrypt.dart';

/// E2E message encryption using AES
abstract class VMessageEncryption {
  // Static encryption key - in production, this should be derived from user keys
  static const String _encryptionKey = "SuperUpE2EEncryptionKey2024!@#\$1234";

  static final _key = Key.fromUtf8(_encryptionKey.substring(0, 32));
  static final _iv = IV.fromLength(16);
  static final _encrypter = Encrypter(AES(_key));

  /// Encrypts a message using AES encryption
  static String encryptMessage(String message) {
    try {
      if (message.isEmpty) return message;

      final encrypted = _encrypter.encrypt(message, iv: _iv);
      return encrypted.base64;
    } catch (e) {
      // If encryption fails, return original message to prevent app crashes
      return message;
    }
  }

  /// Decrypts a message using AES decryption
  static String deCryptMessage(String encryptedMessage) {
    try {
      if (encryptedMessage.isEmpty) return encryptedMessage;

      final encrypted = Encrypted.fromBase64(encryptedMessage);
      final decrypted = _encrypter.decrypt(encrypted, iv: _iv);
      return decrypted;
    } catch (e) {
      // If decryption fails, return original message (might be unencrypted)
      return encryptedMessage;
    }
  }
}
