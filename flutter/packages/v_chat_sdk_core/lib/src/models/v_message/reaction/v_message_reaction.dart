// Copyright 2023, the hatemragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

/// Represents a single reaction on a message
class VMessageReaction {
  /// The emoji used for the reaction
  final String emoji;
  
  /// List of user IDs who reacted with this emoji
  final List<String> userIds;
  
  /// Count of users who reacted with this emoji
  int get count => userIds.length;

  VMessageReaction({
    required this.emoji,
    required this.userIds,
  });

  /// Create from map (from server response)
  factory VMessageReaction.fromMap(Map<String, dynamic> map) {
    return VMessageReaction(
      emoji: map['emoji'] as String,
      userIds: List<String>.from(map['userIds'] as List),
    );
  }

  /// Convert to map (for server request)
  Map<String, dynamic> toMap() {
    return {
      'emoji': emoji,
      'userIds': userIds,
    };
  }

  /// Create a copy with modified user IDs
  VMessageReaction copyWith({
    String? emoji,
    List<String>? userIds,
  }) {
    return VMessageReaction(
      emoji: emoji ?? this.emoji,
      userIds: userIds ?? this.userIds,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is VMessageReaction &&
        other.emoji == emoji &&
        other.userIds.length == userIds.length &&
        other.userIds.every((id) => userIds.contains(id));
  }

  @override
  int get hashCode => emoji.hashCode ^ userIds.hashCode;

  @override
  String toString() {
    return 'VMessageReaction{emoji: $emoji, userIds: $userIds}';
  }
}

/// Helper class to manage reactions on a message
class VMessageReactions {
  /// Map of emoji to reaction data
  final Map<String, VMessageReaction> _reactions;

  VMessageReactions([Map<String, VMessageReaction>? reactions])
      : _reactions = reactions ?? {};

  /// Get all reactions
  List<VMessageReaction> get reactions => _reactions.values.toList();

  /// Get reaction for specific emoji
  VMessageReaction? getReaction(String emoji) => _reactions[emoji];

  /// Check if user has reacted with specific emoji
  bool hasUserReacted(String emoji, String userId) {
    final reaction = _reactions[emoji];
    return reaction?.userIds.contains(userId) ?? false;
  }

  /// Check if user has reacted with any emoji
  bool hasUserReactedWithAny(String userId) {
    return _reactions.values.any((reaction) => reaction.userIds.contains(userId));
  }

  /// Get the emoji the user reacted with (if any)
  String? getUserReactionEmoji(String userId) {
    for (final reaction in _reactions.values) {
      if (reaction.userIds.contains(userId)) {
        return reaction.emoji;
      }
    }
    return null;
  }

  /// Add or remove a user's reaction
  VMessageReactions toggleReaction(String emoji, String userId) {
    final newReactions = Map<String, VMessageReaction>.from(_reactions);
    
    // Remove user's existing reaction if they have one
    for (final entry in newReactions.entries) {
      if (entry.value.userIds.contains(userId)) {
        final updatedUserIds = List<String>.from(entry.value.userIds)..remove(userId);
        if (updatedUserIds.isEmpty) {
          newReactions.remove(entry.key);
        } else {
          newReactions[entry.key] = entry.value.copyWith(userIds: updatedUserIds);
        }
        break;
      }
    }
    
    // Add new reaction if it's different from the removed one
    if (newReactions[emoji]?.userIds.contains(userId) != true) {
      if (newReactions.containsKey(emoji)) {
        final existingReaction = newReactions[emoji]!;
        final updatedUserIds = List<String>.from(existingReaction.userIds)..add(userId);
        newReactions[emoji] = existingReaction.copyWith(userIds: updatedUserIds);
      } else {
        newReactions[emoji] = VMessageReaction(emoji: emoji, userIds: [userId]);
      }
    }
    
    return VMessageReactions(newReactions);
  }

  /// Create from map (from server response)
  factory VMessageReactions.fromMap(Map<String, dynamic>? map) {
    if (map == null) return VMessageReactions();
    
    final reactions = <String, VMessageReaction>{};
    for (final entry in map.entries) {
      reactions[entry.key] = VMessageReaction.fromMap(entry.value as Map<String, dynamic>);
    }
    return VMessageReactions(reactions);
  }

  /// Convert to map (for server request)
  Map<String, dynamic> toMap() {
    final map = <String, dynamic>{};
    for (final entry in _reactions.entries) {
      map[entry.key] = entry.value.toMap();
    }
    return map;
  }

  /// Check if there are any reactions
  bool get isEmpty => _reactions.isEmpty;

  /// Get total reaction count
  int get totalCount => _reactions.values.fold(0, (sum, reaction) => sum + reaction.count);

  @override
  String toString() {
    return 'VMessageReactions{reactions: $_reactions}';
  }
}
