import 'package:flutter_test/flutter_test.dart';
import 'package:v_chat_sdk_core/v_chat_sdk_core.dart';

void main() {
  group('E2E Message Encryption Tests', () {
    test('should encrypt and decrypt text messages correctly', () {
      // Test data
      const originalMessage = 'Hello, this is a test message!';
      const emptyMessage = '';
      const longMessage = 'This is a very long message that contains multiple words and should be encrypted and decrypted correctly without any issues. It includes special characters like @#\$%^&*()!';
      
      // Test normal message
      final encrypted = VMessageEncryption.encryptMessage(originalMessage);
      expect(encrypted, isNot(equals(originalMessage))); // Should be different
      expect(encrypted.isNotEmpty, true); // Should not be empty
      
      final decrypted = VMessageEncryption.deCryptMessage(encrypted);
      expect(decrypted, equals(originalMessage)); // Should match original
      
      // Test empty message
      final encryptedEmpty = VMessageEncryption.encryptMessage(emptyMessage);
      expect(encryptedEmpty, equals(emptyMessage)); // Empty should remain empty
      
      final decryptedEmpty = VMessageEncryption.deCryptMessage(encryptedEmpty);
      expect(decryptedEmpty, equals(emptyMessage));
      
      // Test long message
      final encryptedLong = VMessageEncryption.encryptMessage(longMessage);
      expect(encryptedLong, isNot(equals(longMessage)));
      
      final decryptedLong = VMessageEncryption.deCryptMessage(encryptedLong);
      expect(decryptedLong, equals(longMessage));
    });
    
    test('should handle encryption errors gracefully', () {
      // Test with invalid encrypted data
      const invalidEncrypted = 'invalid_base64_data';
      final result = VMessageEncryption.deCryptMessage(invalidEncrypted);
      
      // Should return original data if decryption fails
      expect(result, equals(invalidEncrypted));
    });
    
    test('should produce consistent encryption results', () {
      const message = 'Test consistency';
      
      final encrypted1 = VMessageEncryption.encryptMessage(message);
      final encrypted2 = VMessageEncryption.encryptMessage(message);
      
      // With fixed IV, results should be the same
      expect(encrypted1, equals(encrypted2));
      
      // Both should decrypt to the same original message
      expect(VMessageEncryption.deCryptMessage(encrypted1), equals(message));
      expect(VMessageEncryption.deCryptMessage(encrypted2), equals(message));
    });
  });
}
