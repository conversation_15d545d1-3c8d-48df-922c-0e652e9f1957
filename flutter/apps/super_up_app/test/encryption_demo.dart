import 'package:flutter_test/flutter_test.dart';
import 'package:v_chat_sdk_core/v_chat_sdk_core.dart';

void main() {
  group('E2E Encryption Demo', () {
    test('demonstrate message encryption and decryption', () {
      print('\n🔐 E2E Message Encryption Demo');
      print('=' * 50);
      
      // Example messages to encrypt
      final messages = [
        'Hello, this is a secret message!',
        'Meeting at 3 PM today 📅',
        'The password is: SuperSecret123!',
        'Can you send me the project files?',
        'Thanks for the help! 😊',
      ];
      
      print('\n📝 Original Messages:');
      for (int i = 0; i < messages.length; i++) {
        print('${i + 1}. "${messages[i]}"');
      }
      
      print('\n🔒 Encrypted Messages:');
      final encryptedMessages = <String>[];
      for (int i = 0; i < messages.length; i++) {
        final encrypted = VMessageEncryption.encryptMessage(messages[i]);
        encryptedMessages.add(encrypted);
        print('${i + 1}. ${encrypted}');
      }
      
      print('\n🔓 Decrypted Messages (verification):');
      for (int i = 0; i < encryptedMessages.length; i++) {
        final decrypted = VMessageEncryption.deCryptMessage(encryptedMessages[i]);
        print('${i + 1}. "${decrypted}"');
        
        // Verify decryption worked correctly
        expect(decrypted, equals(messages[i]));
      }
      
      print('\n✅ All messages encrypted and decrypted successfully!');
      print('\n📊 Encryption Statistics:');
      print('- Original total length: ${messages.map((m) => m.length).reduce((a, b) => a + b)} characters');
      print('- Encrypted total length: ${encryptedMessages.map((m) => m.length).reduce((a, b) => a + b)} characters');
      print('- Encryption overhead: ~${((encryptedMessages.first.length / messages.first.length) * 100).toStringAsFixed(1)}%');
      
      print('\n🔍 Technical Details:');
      print('- Algorithm: AES-256');
      print('- Mode: CBC with fixed IV');
      print('- Encoding: Base64');
      print('- Key: Static 32-byte key (for demo)');
      
      print('\n' + '=' * 50);
    });
    
    test('show how messages look in different states', () {
      const originalMessage = 'This is a confidential message 🔒';
      
      print('\n🔄 Message Processing Flow');
      print('=' * 40);
      
      print('\n1️⃣ User types message:');
      print('   "$originalMessage"');
      
      print('\n2️⃣ App encrypts before sending:');
      final encrypted = VMessageEncryption.encryptMessage(originalMessage);
      print('   $encrypted');
      
      print('\n3️⃣ Server stores encrypted data:');
      print('   Database content: "$encrypted"');
      
      print('\n4️⃣ Recipient receives and decrypts:');
      final decrypted = VMessageEncryption.deCryptMessage(encrypted);
      print('   "$decrypted"');
      
      print('\n✅ End-to-end encryption complete!');
      print('   Only sender and recipient can read the message.');
      print('   Server only sees encrypted data.');
      
      // Verify the process worked
      expect(decrypted, equals(originalMessage));
    });
  });
}
